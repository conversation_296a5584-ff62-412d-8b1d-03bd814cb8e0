-- Полный скрипт восстановления данных после отката миграции 0007_auto_20240401_1007
-- Восстанавливает:
-- 1. Столбец dm_tindex в таблице department
-- 2. Таблицу user_region_access 
-- 3. Таблицу adr.product_category_layer

BEGIN;

-- ========================================
-- 1. Восстановление dm_tindex в department
-- ========================================
DO $$
BEGIN
    RAISE NOTICE 'Начинаем восстановление dm_tindex...';
END $$;

CREATE TEMP TABLE temp_department_data (
    id INTEGER,
    name TEXT,
    code TEXT,
    post_code TEXT,
    dm_tindex TEXT,
    lat FLOAT,
    lon FLOAT,
    status INTEGER,
    region_id INTEGER,
    class_field TEXT,
    location_name TEXT,
    type_id INTEGER,
    new_post_code TEXT,
    sub_department_id INTEGER
);

COPY temp_department_data FROM 'C:/Dara/Python Project/geoservice/kosyak/department.csv' 
WITH (FORMAT csv, DELIMITER ',', NULL '');

UPDATE department 
SET dm_tindex = temp_department_data.dm_tindex
FROM temp_department_data
WHERE department.id = temp_department_data.id;

DROP TABLE temp_department_data;

-- ========================================
-- 2. Восстановление user_region_access
-- ========================================
DO $$
BEGIN
    RAISE NOTICE 'Начинаем восстановление user_region_access...';
END $$;

CREATE TEMP TABLE temp_user_region_access (
    id INTEGER,
    user_id INTEGER,
    region_id INTEGER,
    has_full_access BOOLEAN
);

COPY temp_user_region_access FROM 'C:/Dara/Python Project/geoservice/kosyak/user_region_access.csv' 
WITH (FORMAT csv, DELIMITER ',', NULL '');

INSERT INTO user_region_access (user_id, region_id, has_full_access)
SELECT 
    user_id,
    region_id,
    COALESCE(has_full_access, false)
FROM temp_user_region_access
ON CONFLICT DO NOTHING;

DROP TABLE temp_user_region_access;

-- ========================================
-- 3. Восстановление adr.product_category_layer
-- ========================================
DO $$
BEGIN
    RAISE NOTICE 'Начинаем восстановление product_category_layer...';
END $$;

CREATE TEMP TABLE temp_product_category_layer (
    product_code TEXT,
    product_name TEXT,
    layer_id INTEGER
);

COPY temp_product_category_layer FROM 'C:/Dara/Python Project/geoservice/kosyak/product_category_layer.csv' 
WITH (FORMAT csv, DELIMITER ',', NULL '');

INSERT INTO adr.product_category_layer (product_code, product_name, layer_id)
SELECT 
    product_code,
    COALESCE(product_name, ''),
    layer_id
FROM temp_product_category_layer
ON CONFLICT (product_code) DO UPDATE SET
    product_name = EXCLUDED.product_name,
    layer_id = EXCLUDED.layer_id;

DROP TABLE temp_product_category_layer;

-- ========================================
-- Финальная статистика
-- ========================================
DO $$
BEGIN
    RAISE NOTICE 'Восстановление завершено! Статистика:';
END $$;

SELECT 'department dm_tindex' as table_name, 
       COUNT(*) as total_records,
       COUNT(CASE WHEN dm_tindex IS NOT NULL AND dm_tindex != '------' THEN 1 END) as with_data
FROM department
UNION ALL
SELECT 'user_region_access' as table_name,
       COUNT(*) as total_records,
       COUNT(CASE WHEN has_full_access = true THEN 1 END) as with_full_access
FROM user_region_access
UNION ALL
SELECT 'product_category_layer' as table_name,
       COUNT(*) as total_records,
       COUNT(CASE WHEN layer_id IS NOT NULL THEN 1 END) as with_layer
FROM adr.product_category_layer;

COMMIT;
