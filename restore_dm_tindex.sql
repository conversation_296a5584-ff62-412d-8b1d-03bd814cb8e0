-- Скрипт для восстановления столбца dm_tindex в таблице department
-- Данные берутся из файла kosyak/department.csv

-- Создаем временную таблицу для загрузки CSV данных
CREATE TEMP TABLE temp_department_data (
    id INTEGER,
    name TEXT,
    code TEXT,
    post_code TEXT,
    dm_tindex TEXT,
    lat FLOAT,
    lon FLOAT,
    status INTEGER,
    region_id INTEGER,
    class_field TEXT,
    location_name TEXT,
    type_id INTEGER,
    new_post_code TEXT,
    sub_department_id INTEGER
);

-- Загружаем данные из CSV файла
-- ВАЖНО: Путь к файлу нужно будет изменить на абсолютный путь
COPY temp_department_data FROM 'C:/Dara/Python Project/geoservice/kosyak/department.csv' 
WITH (FORMAT csv, DELIMITER ',', NULL '');

-- Обновляем столбец dm_tindex в основной таблице department
UPDATE department 
SET dm_tindex = temp_department_data.dm_tindex
FROM temp_department_data
WHERE department.id = temp_department_data.id;

-- Показываем статистику обновления
SELECT 
    COUNT(*) as total_updated,
    COUNT(CASE WHEN dm_tindex IS NOT NULL AND dm_tindex != '------' THEN 1 END) as with_tindex
FROM department;

-- Удаляем временную таблицу
DROP TABLE temp_department_data;
