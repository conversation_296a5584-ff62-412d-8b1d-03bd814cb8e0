-- Скрипт для восстановления таблицы user_region_access
-- Данные берутся из файла kosyak/user_region_access.csv

-- Создаем временную таблицу для загрузки CSV данных
CREATE TEMP TABLE temp_user_region_access (
    id INTEGER,
    user_id INTEGER,
    region_id INTEGER,
    has_full_access BOOLEAN
);

-- Загружаем данные из CSV файла
-- ВАЖНО: Путь к файлу нужно будет изменить на абсолютный путь
COPY temp_user_region_access FROM 'C:/Dara/Python Project/geoservice/kosyak/user_region_access.csv' 
WITH (FORMAT csv, DELIMITER ',', NULL '');

-- Очищаем таблицу user_region_access (если нужно)
-- TRUNCATE TABLE user_region_access;

-- Вставляем данные в основную таблицу
INSERT INTO user_region_access (user_id, region_id, has_full_access)
SELECT 
    user_id,
    region_id,
    COALESCE(has_full_access, false)
FROM temp_user_region_access
ON CONFLICT DO NOTHING; -- Избегаем дублирования, если записи уже существуют

-- Показываем статистику
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN has_full_access = true THEN 1 END) as full_access_count,
    COUNT(CASE WHEN has_full_access = false THEN 1 END) as limited_access_count
FROM user_region_access;

-- Удаляем временную таблицу
DROP TABLE temp_user_region_access;
