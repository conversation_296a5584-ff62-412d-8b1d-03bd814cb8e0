-- Скрипт для восстановления таблицы adr.product_category_layer
-- Данные берутся из файла kosyak/product_category_layer.csv

-- Создаем временную таблицу для загрузки CSV данных
CREATE TEMP TABLE temp_product_category_layer (
    product_code TEXT,
    product_name TEXT,
    layer_id INTEGER
);

-- Загружаем данные из CSV файла
-- ВАЖНО: Путь к файлу нужно будет изменить на абсолютный путь
COPY temp_product_category_layer FROM 'C:/Dara/Python Project/geoservice/kosyak/product_category_layer.csv' 
WITH (FORMAT csv, DELIMITER ',', NULL '');

-- Очищаем таблицу adr.product_category_layer (если нужно)
-- TRUNCATE TABLE adr.product_category_layer;

-- Вставляем данные в основную таблицу
INSERT INTO adr.product_category_layer (product_code, product_name, layer_id)
SELECT 
    product_code,
    COALESCE(product_name, ''),
    layer_id
FROM temp_product_category_layer
ON CONFLICT (product_code) DO UPDATE SET
    product_name = EXCLUDED.product_name,
    layer_id = EXCLUDED.layer_id;

-- Показываем статистику
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN layer_id IS NOT NULL THEN 1 END) as with_layer,
    COUNT(CASE WHEN layer_id IS NULL THEN 1 END) as without_layer
FROM adr.product_category_layer;

-- Удаляем временную таблицу
DROP TABLE temp_product_category_layer;
